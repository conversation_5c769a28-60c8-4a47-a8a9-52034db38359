<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>KNKA Air Purifier Showcase</title>
<style>
  /* 基本样式 */
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', Helvetica, Arial, sans-serif;
    color: #1d1d1f;
    line-height: 1.47059;
    overflow-x: hidden;
  }
  
  /* 基本样式 */
  .aphm3-iphone-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding: 40px 20px 0;
    width: 100%;
    margin: 0 auto;
    text-align: center;
    transition: transform 0.3s ease;
    background-color: #f5f5f7;
    aspect-ratio: 2.1 / 1;
    overflow: hidden;
    position: relative;
    box-sizing: border-box;
  }
  
  .aphm3-iphone-container:hover {
    transform: scale(1.09); /* 将元素放大2% */
  }
  
  .aphm3-content-wrapper {
    margin-bottom: 20px;
    z-index: 2;
    padding-top: 3%;
    width: 100%;
  }
  
  .aphm3-title {
    font-size: 48px;
    font-weight: 600;
    margin: 0 0 10px;
    color: #1d1d1f;
  }
  
  .aphm3-subtitle {
    font-size: 28px;
    font-weight: 400;
    margin: 0 0 25px;
    color: #1d1d1f;
  }
  
  .aphm3-buttons-container {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
  }
  
  .aphm3-button {
    display: inline-block;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 16px;
    font-weight: 400;
    text-decoration: none;
    transition: all 0.3s ease;
  }
  
  .aphm3-button-primary {
    background-color: #0071e3;
    color: white;
  }
  
  .aphm3-button-primary:hover {
    background-color: #0062c3;
    transform: scale(1.05);
  }
  
  .aphm3-button-secondary {
    background-color: white;
    color: #0071e3;
    border: 1px solid #0071e3;
  }
  
  .aphm3-button-secondary:hover {
    background-color: rgba(0, 113, 227, 0.1);
    transform: scale(1.05);
  }
  
  .aphm3-tagline {
    font-size: 18px;
    margin: 0;
    color: #1d1d1f;
  }
  
  .aphm3-highlight {
    background: linear-gradient(90deg, #0071e3, #bf4ec8);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 500;
  }
  
  .aphm3-image-container {
    width: 100%;
    height: auto;
    position: relative;
    flex-grow: 1;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    overflow: hidden;
  }
  
  .aphm3-iphone-image {
    width: 65%;
    max-width: 900px;
    max-height: 100%;
    object-fit: contain;
    object-position: bottom center;
    display: block;
    margin-bottom: -2%;
  }
  
  /* 响应式设计 */
  @media (min-width: 1601px) {
    .aphm3-iphone-image {
      width: 55%;
    }
  }
  
  @media (max-width: 1600px) and (min-width: 1401px) {
    .aphm3-iphone-image {
      width: 60%;
    }
  }
  
  @media (max-width: 1400px) and (min-width: 1201px) {
    .aphm3-iphone-image {
      width: 65%;
    }
  }
  
  @media (max-width: 1200px) and (min-width: 993px) {
    .aphm3-iphone-container {
      aspect-ratio: 2 / 1;
    }
    
    .aphm3-content-wrapper {
      padding-top: 2.5%;
      margin-bottom: 15px;
    }
    
    .aphm3-iphone-image {
      width: 70%;
      margin-bottom: -1%;
    }
  }
  
  @media (max-width: 992px) and (min-width: 769px) {
    .aphm3-iphone-container {
      padding-bottom: 20px;
      aspect-ratio: 1.9 / 1;
    }
    
    .aphm3-content-wrapper {
      padding-top: 2%;
      margin-bottom: 10px;
    }
    
    .aphm3-image-container {
      height: auto;
    }
    
    .aphm3-iphone-image {
      width: 75%;
      margin-bottom: 0;
    }
  }
  
  @media (max-width: 768px) {
    .aphm3-iphone-container {
      aspect-ratio: auto;
      min-height: 500px;
      padding-bottom: 30px;
    }
    
    .aphm3-content-wrapper {
      padding-top: 5%;
    }
    
    .aphm3-title {
      font-size: 36px;
    }
    
    .aphm3-subtitle {
      font-size: 22px;
    }
    
    .aphm3-buttons-container {
      flex-direction: column;
      gap: 10px;
    }
    
    .aphm3-button {
      width: 80%;
      margin: 0 auto;
    }
    
    .aphm3-image-container {
      margin-top: 20px;
    }
    
    .aphm3-iphone-image {
      width: 90%;
      margin-bottom: 0;
    }
  }
  
  @media (max-width: 480px) {
    .aphm3-iphone-container {
      min-height: 450px;
    }
    
    .aphm3-title {
      font-size: 28px;
    }
    
    .aphm3-subtitle {
      font-size: 18px;
    }
    
    .aphm3-tagline {
      font-size: 16px;
    }
    
    .aphm3-iphone-image {
      width: 100%;
    }
  }

  /* 弹窗样式 - 优化后的样式 */
  /* 毛玻璃背景 */
  .aphm3-modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    z-index: 999;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  /* 弹出卡片样式 */
  .aphm3-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.95);
    width: 90%;
    max-width: 1000px;
    max-height: 85vh;
    background-color: #fff;
    z-index: 1000;
    display: none;
    opacity: 0;
    transition: all 0.3s ease;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border-radius: 16px;
    overflow: hidden;
  }

  /* 弹出卡片内容 */
  .aphm3-modal-content {
    padding: 24px;
    height: 100%;
    max-height: 85vh;
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    display: flex;
    flex-direction: column;
  }

  /* 隐藏WebKit浏览器的滚动条 */
  .aphm3-modal-content::-webkit-scrollbar {
    display: none;
  }

  /* 产品类别标题 */
  .aphm3-modal-category {
    font-size: 14px;
    line-height: 1.42859;
    font-weight: 400;
    letter-spacing: -0.016em;
    color: #86868b;
    margin-bottom: 12px;
    margin-left: 4px;
  }

  /* 弹出卡片主体 - 左右布局 */
  .aphm3-modal-body {
    display: flex;
    flex-direction: row;
    gap: 32px;
    width: 100%;
    min-height: 380px;
  }

  /* 左侧产品图片区域 */
  .aphm3-modal-left {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0;
  }

  /* 产品图片容器 */
  .aphm3-modal-image {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    border-radius: 8px;
  }

  .aphm3-modal-image img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }

  /* 右侧产品信息区域 */
  .aphm3-modal-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 4px 0;
  }

  /* 产品标题 */
  .aphm3-modal-product-title {
    font-size: 36px;
    line-height: 1.1;
    font-weight: 600;
    letter-spacing: -0.002em;
    color: #1d1d1f;
    margin: 0 0 16px 0;
  }

  /* 价格和购买按钮容器 */
  .aphm3-modal-price-buy-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
  }

  /* 产品价格 */
  .aphm3-modal-price {
    font-size: 17px;
    line-height: 1.47059;
    font-weight: 400;
    color: #1d1d1f;
    margin: 0;
  }

  /* 弹出卡片内的购买按钮 */
  .aphm3-modal-buy {
    background: #0071e3;
    color: #fff;
    border-radius: 980px;
    font-size: 15px;
    line-height: 1.47059;
    font-weight: 500;
    padding: 6px 16px;
    text-decoration: none;
    text-align: center;
    display: inline-block;
    cursor: pointer;
    border: none;
    transition: all 0.2s ease;
    width: 80px;
  }

  .aphm3-modal-buy:hover {
    background-color: #0077ed;
  }

  /* 产品特性列表 */
  .aphm3-modal-features {
    display: flex;
    flex-direction: column;
    gap: 0;
  }

  /* 产品特性项 */
  .aphm3-feature-item {
    display: flex;
    gap: 12px;
    padding: 12px 0;
    border-top: 1px solid #d2d2d7;
  }

  .aphm3-feature-item:first-child {
    border-top: none;
    padding-top: 0;
  }

  /* 特性图标 */
  .aphm3-feature-icon {
    width: 32px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
  }

  .aphm3-feature-icon svg {
    width: 24px;
    height: 24px;
  }

  /* 特性文本 */
  .aphm3-feature-content {
    flex: 1;
  }

  .aphm3-feature-title {
    font-size: 15px;
    line-height: 1.4;
    font-weight: 600;
    margin-bottom: 4px;
    color: #1d1d1f;
  }

  .aphm3-feature-text {
    font-size: 15px;
    line-height: 1.4;
    color: #1d1d1f;
  }

  /* 上标样式 */
  .aphm3-superscript {
    font-size: 0.6em;
    vertical-align: super;
  }

  /* 关闭按钮 */
  .aphm3-modal-close {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border: none;
    z-index: 10;
    transition: background-color 0.2s ease;
    padding: 0;
    margin: 0;
  }

  .aphm3-modal-close:hover {
    background-color: #000;
  }

  .aphm3-modal-close svg {
    width: 10px;
    height: 10px;
    display: block;
  }

  /* 显示弹出卡片和背景 */
  .aphm3-modal.active {
    display: block;
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }

  .aphm3-modal-backdrop.active {
    display: block;
    opacity: 1;
  }

  /* 响应式调整 */
  @media only screen and (max-width: 1068px) {
    .aphm3-modal-product-title {
      font-size: 28px;
    }
    
    .aphm3-modal-body {
      gap: 24px;
    }
  }

  @media only screen and (max-width: 734px) {
    .aphm3-modal-product-title {
      font-size: 24px;
      margin-bottom: 12px;
    }
    
    .aphm3-modal-content {
      padding: 20px;
    }
    
    /* 移动端弹窗布局调整为垂直方向 */
    .aphm3-modal-body {
      flex-direction: column;
      gap: 16px;
      min-height: auto;
    }
    
    /* 移动端下图片区域添加底部间距 */
    .aphm3-modal-left {
      margin-bottom: 0;
      max-height: 200px;
    }
    
    .aphm3-feature-item {
      padding: 10px 0;
    }
    
    .aphm3-feature-icon {
      width: 28px;
      height: 28px;
    }
    
    .aphm3-feature-icon svg {
      width: 20px;
      height: 20px;
    }
    
    .aphm3-feature-title {
      font-size: 14px;
    }
    
    .aphm3-feature-text {
      font-size: 14px;
    }
  }
</style>
</head>
<body>

<div class="aphm3-iphone-container">
  <div class="aphm3-content-wrapper">
    <h1 class="aphm3-title">KNKA Air Purifier</h1>
    <h2 class="aphm3-subtitle">APH3000</h2>
    
    <div class="aphm3-buttons-container">
      <a href="#" class="aphm3-button aphm3-button-primary" data-modal="airpurifier-modal">Learn more</a>
      <a href="#" class="aphm3-button aphm3-button-secondary">Shop Here</a>
    </div>
    
    <p class="aphm3-tagline"><span class="aphm3-highlight">Keep Natural Keep Advancing</span>.</p>
  </div>
  
  <div class="aphm3-image-container">
    <img src="https://knkalife.com/wp-content/uploads/2025/05/3000-大图png.png" alt="KNKA Air Purifier APH3000" class="aphm3-iphone-image">
  </div>
</div>

<!-- 毛玻璃背景 -->
<div class="aphm3-modal-backdrop" id="modal-backdrop"></div>

<!-- Air Purifier APH3000 弹出卡片 -->
<div class="aphm3-modal" id="airpurifier-modal">
  <div class="aphm3-modal-content">
    <p class="aphm3-modal-category">Air Purifier</p>
    
    <div class="aphm3-modal-body">
      <div class="aphm3-modal-left">
        <div class="aphm3-modal-image">
          <img src="https://knkalife.com/wp-content/uploads/2025/05/主图3-1.jpg" alt="Air Purifier APH3000">
        </div>
      </div>
      
      <div class="aphm3-modal-right">
        <h2 class="aphm3-modal-product-title">APH3000</h2>
        <div class="aphm3-modal-price-buy-container">
          <p class="aphm3-modal-price"></p>
          <a href="#" class="aphm3-modal-buy">Buy</a>
        </div>
        
        <div class="aphm3-modal-features">
          <div class="aphm3-feature-item">
            <div class="aphm3-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="#1d1d1f" stroke-width="2"/>
                <path d="M12 6V12L16 14" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </div>
            <div class="aphm3-feature-content">
              <p class="aphm3-feature-text"><strong>Turbo Mode, Pure Air, Fast.</strong><br/>Boost purification speed by over 30%. Includes child lock, aroma sponge, and sleek, pet-friendly design.</p>
            </div>
          </div>
          
          <div class="aphm3-feature-item">
            <div class="aphm3-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#1d1d1f" stroke-width="2"/>
                <path d="M12 7V12L15 15" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="aphm3-feature-content">
              <p class="aphm3-feature-text"><strong>Pure Air, Wide Reach.</strong><br/>True HEPA cleans spaces up to 2,325 sq. ft. Powerful circulation ensures fresh, healthy air wherever you need it.</p>
            </div>
          </div>
          
          <div class="aphm3-feature-item">
            <div class="aphm3-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 12L11 14L15 10" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#1d1d1f" stroke-width="2"/>
              </svg>
            </div>
            <div class="aphm3-feature-content">
              <p class="aphm3-feature-text"><strong>Triple Filtration, Lasting Clean.</strong><br/>Washable pre-filter protects the H13 HEPA, while activated carbon traps 99.9% of particles down to 0.3 microns.</p>
            </div>
          </div>
          
          <div class="aphm3-feature-item">
            <div class="aphm3-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 18C15.3137 18 18 15.3137 18 12C18 8.68629 15.3137 6 12 6C8.68629 6 6 8.68629 6 12C6 15.3137 8.68629 18 12 18Z" stroke="#1d1d1f" stroke-width="2"/>
                <path d="M12 2V4" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round"/>
                <path d="M12 20V22" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round"/>
                <path d="M4 12H2" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round"/>
                <path d="M22 12H20" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </div>
            <div class="aphm3-feature-content">
              <p class="aphm3-feature-text"><strong>Smart Sensing, Automatic Precision.</strong><br/>Continuous air quality monitoring with real-time LED updates. Auto Mode adjusts fan speed for optimal clean and efficiency.</p>
            </div>
          </div>
          
          <div class="aphm3-feature-item">
            <div class="aphm3-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 18V12C3 9.61305 3.94821 7.32387 5.63604 5.63604C7.32387 3.94821 9.61305 3 12 3C14.3869 3 16.6761 3.94821 18.364 5.63604C20.0518 7.32387 21 9.61305 21 12V18" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M21 19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H18C17.4696 21 16.9609 20.7893 16.5858 20.4142C16.2107 20.0391 16 19.5304 16 19V16C16 15.4696 16.2107 14.9609 16.5858 14.5858C16.9609 14.2107 17.4696 14 18 14H21V19ZM3 19C3 19.5304 3.21071 20.0391 3.58579 20.4142C3.96086 20.7893 4.46957 21 5 21H6C6.53043 21 7.03914 20.7893 7.41421 20.4142C7.78929 20.0391 8 19.5304 8 19V16C8 15.4696 7.78929 14.9609 7.41421 14.5858C7.03914 14.2107 6.53043 14 6 14H3V19Z" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="aphm3-feature-content">
              <p class="aphm3-feature-text"><strong>Whisper-Quiet, Restful Sleep.</strong><br/>Operates at 24dB with adjustable lights, ensuring peaceful nights and continuous purification.</p>
            </div>
          </div>
          
          <div class="aphm3-feature-item">
            <div class="aphm3-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 18C15.3137 18 18 15.3137 18 12C18 8.68629 15.3137 6 12 6C8.68629 6 6 8.68629 6 12C6 15.3137 8.68629 18 12 18Z" stroke="#1d1d1f" stroke-width="2"/>
                <path d="M12 8V12L15 15" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M19 12H21" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round"/>
                <path d="M3 12H5" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round"/>
                <path d="M12 19V21" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round"/>
                <path d="M12 3V5" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </div>
            <div class="aphm3-feature-content">
              <p class="aphm3-feature-text"><strong>Tailored Airflow, Intelligent Timing.</strong><br/>Three fan speeds plus Auto Mode that adapts to air quality. Set the timer for up to 12 hours for effortless control.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <button class="aphm3-modal-close" aria-label="Close">
      <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1 1L11 11M11 1L1 11" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
      </svg>
    </button>
  </div>
</div>

<script>
  // Elementor兼容的初始化函数
  function initializeElements() {
    console.log('正在初始化元素...');

    // 获取所有元素
    const learnMoreButtons = document.querySelectorAll('[data-modal]');
    const modalBackdrop = document.getElementById('modal-backdrop');
    const modals = document.querySelectorAll('.aphm3-modal');
    const closeButtons = document.querySelectorAll('.aphm3-modal-close');
    let buyButton = document.querySelector('.aphm3-button-secondary'); // 主页购买按钮
    let modalBuyButton = document.querySelector('.aphm3-modal-buy'); // 模态框购买按钮

    console.log('buyButton found:', buyButton);
    console.log('modalBuyButton found:', modalBuyButton);

    // 如果主要按钮不存在，等待一段时间后重试
    if (!buyButton) {
      console.log('buyButton未找到，1秒后重试...');
      setTimeout(initializeElements, 1000);
      return;
    }

    // 打开弹出卡片
    learnMoreButtons.forEach(button => {
      button.addEventListener('click', function(e) {
        e.preventDefault();
        const modalId = this.getAttribute('data-modal');
        const modal = document.getElementById(modalId);

        // 显示毛玻璃背景和弹出卡片
        modalBackdrop.classList.add('active');
        modal.classList.add('active');

        // 禁止背景滚动
        document.body.style.overflow = 'hidden';
      });
    });

    // 关闭弹出卡片
    function closeModal() {
      modalBackdrop.classList.remove('active');
      modals.forEach(modal => {
        modal.classList.remove('active');
      });

      // 恢复背景滚动
      document.body.style.overflow = '';
    }

    // 点击关闭按钮
    closeButtons.forEach(button => {
      button.addEventListener('click', closeModal);
    });

    // 点击背景关闭弹出卡片
    modalBackdrop.addEventListener('click', closeModal);

    // 阻止弹出卡片内点击事件冒泡到背景
    modals.forEach(modal => {
      modal.addEventListener('click', function(e) {
        e.stopPropagation();
      });
    });

    // ESC键关闭弹出卡片
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
        closeModal();
      }
    });

    // 1. 扩展的多语言购买链接配置
    const buyLinks = {
        // 英语国家细分
        'en-US': 'https://www.amazon.com/dp/B0D9PTN74F',
        'en-GB': 'https://amazon.co.uk/knka-dehumidifier',
        'en-CA': 'https://www.amazon.ca/dp/B0D9PTN74F',
        'en-AU': 'https://amazon.com.au/knka-dehumidifier',
        'en-NZ': 'https://amazon.com.au/knka-dehumidifier', // 新西兰用澳洲链接

        // 其他语言保持不变
        'es': 'https://www.xibanya.com',
        'de': 'https://www.amazon.de/dp',
        'fr': 'https://www.faguo.com',
        'it': 'https://www.yidaliyu.com',

        // 默认英语
        'en': 'https://www.amazon.com/dp/B0D9PTN74F'
    };

    // 2. 获取用户国家的函数
    async function getUserCountry() {
        try {
            const response = await fetch('https://ipapi.co/json/');
            const data = await response.json();
            return data.country_code;
        } catch (error) {
            console.log('IP检测失败，使用默认美国链接');
            return 'US';
        }
    }

    // 3. 更新的购买按钮链接函数（同时更新主页和模态框按钮）
    async function updateBuyButtonLink() {
        const currentBuyButton = document.querySelector('.aphm3-button-secondary');
        const currentModalBuyButton = document.querySelector('.aphm3-modal-buy');

        if (!currentBuyButton) {
            console.error("Buy button with class '.aphm3-button-secondary' not found.");
            return;
        }

        const currentLang = document.documentElement.lang.split('-')[0];
        let targetLink;

        // 先设置默认链接，避免显示#
        const defaultLink = buyLinks['en'] || 'https://amazon.com/knka-dehumidifier';
        currentBuyButton.href = defaultLink;
        if (currentModalBuyButton) {
            currentModalBuyButton.href = defaultLink;
        }
        console.log('设置默认链接:', defaultLink);

        if (currentLang === 'en') {
            // 英语时进一步检测国家
            try {
                const country = await getUserCountry();
                const langCountry = `en-${country}`;
                targetLink = buyLinks[langCountry] || buyLinks['en'];
                console.log(`English detected. Country: ${country}, Using link: ${targetLink}`);
            } catch (error) {
                console.log('获取国家信息失败，使用默认英语链接');
                targetLink = buyLinks['en'];
            }
        } else {
            // 非英语直接使用语言代码
            targetLink = buyLinks[currentLang] || buyLinks['en'];
            console.log(`Language: ${currentLang}, Using link: ${targetLink}`);
        }

        // 同时更新两个按钮的链接
        currentBuyButton.href = targetLink;
        if (currentModalBuyButton) {
            currentModalBuyButton.href = targetLink;
            console.log('模态框按钮链接也已更新:', currentModalBuyButton.href);
        }
        console.log('主页按钮最终链接:', currentBuyButton.href);
    }

    // 4. 使用 MutationObserver 监视 <html> 标签的 lang 属性变化
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'lang') {
                updateBuyButtonLink(); // 当 lang 属性变化时，调用更新函数
            }
        });
    });

    // 5. 配置并启动观察器，让它开始监视
    observer.observe(document.documentElement, {
        attributes: true // 我们只关心属性的变化
    });

    // 6. 页面加载完成后，立即执行一次，以正确设置初始的购买链接
    updateBuyButtonLink();
  }

  // 多种方式确保代码执行，适配Elementor环境
  document.addEventListener('DOMContentLoaded', initializeElements);
  window.addEventListener('load', initializeElements);

  // 如果页面已经加载完成，立即执行
  if (document.readyState === 'complete' || document.readyState === 'interactive') {
    setTimeout(initializeElements, 100);
  }
</script>

</body>
</html>
