---
type: "always_apply"
description: "Example description"
---
####这个项目里所有的html文件都是wordpress里的elementor元素的代码
###所有的html都参考下面的script增加语言和ip切换不同链接的功能
###注意有的块没有弹窗的购买按钮，所以要适当的减少弹窗buy按钮的跳转
###以下是当前的所有链接地址，没有的就保持当前就可以
-美国链接地址
ap2000wf	https://www.amazon.com/dp/B0D985LMQC?th=1
aph3000	https://www.amazon.com/dp/B0D9PTN74F
pd22sc	https://www.amazon.com/dp/B0DR7TZGV8
d032	https://www.amazon.com/dp/B0F6741ZPN
-德国链接地址
pd22sc https://www.amazon.de/dp/B0DZXKLNVL
d032 https://www.amazon.de/dp/B0DZXDCXNL
-加拿大链接地址
ap2000wf https://www.amazon.ca/dp/B0D985LMQC?th=1
aph3000 https://www.amazon.ca/dp/B0D9PTN74F
pd22sc https://www.amazon.ca/dp/B0DR7TZGV8
d032 https://www.amazon.ca/dp/B0F6741ZPN
<script>
  // Elementor兼容的初始化函数
  function initializeElements() {
    console.log('正在初始化元素...');

    // 获取所有元素
    const learnMoreButtons = document.querySelectorAll('[data-modal]');
    const modalBackdrop = document.getElementById('modal-backdrop');
    const modals = document.querySelectorAll('.aphm1-modal');
    const closeButtons = document.querySelectorAll('.aphm1-modal-close');
    let buyButton = document.querySelector('.aphm1-button-secondary'); // 主页购买按钮
    let modalBuyButton = document.querySelector('.aphm1-modal-buy'); // 模态框购买按钮

    console.log('buyButton found:', buyButton);
    console.log('modalBuyButton found:', modalBuyButton);

    // 如果主要按钮不存在，等待一段时间后重试
    if (!buyButton) {
      console.log('buyButton未找到，1秒后重试...');
      setTimeout(initializeElements, 1000);
      return;
    }
    
    // 打开弹出卡片
    learnMoreButtons.forEach(button => {
      button.addEventListener('click', function(e) {
        e.preventDefault();
        const modalId = this.getAttribute('data-modal');
        const modal = document.getElementById(modalId);
        
        // 显示毛玻璃背景和弹出卡片
        modalBackdrop.classList.add('active');
        modal.classList.add('active');
        
        // 禁止背景滚动
        document.body.style.overflow = 'hidden';
      });
    });
    
    // 关闭弹出卡片
    function closeModal() {
      modalBackdrop.classList.remove('active');
      modals.forEach(modal => {
        modal.classList.remove('active');
      });
      
      // 恢复背景滚动
      document.body.style.overflow = '';
    }
    
    // 点击关闭按钮
    closeButtons.forEach(button => {
      button.addEventListener('click', closeModal);
    });
    
    // 点击背景关闭弹出卡片
    modalBackdrop.addEventListener('click', closeModal);
    
    // 阻止弹出卡片内点击事件冒泡到背景
    modals.forEach(modal => {
      modal.addEventListener('click', function(e) {
        e.stopPropagation();
      });
    });
    
    // ESC键关闭弹出卡片
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
        closeModal();
      }
    });
    
    // 1. 扩展的多语言购买链接配置
    const buyLinks = {
        // 英语国家细分
        'en-US': 'https://www.amazon.com/dp/B0DR7TZGV8',
        'en-GB': 'https://amazon.co.uk/knka-dehumidifier', 
        'en-CA': 'https://www.amazon.ca/dp/B0DR7TZGV8',
        'en-AU': 'https://amazon.com.au/knka-dehumidifier',
        'en-NZ': 'https://amazon.com.au/knka-dehumidifier', // 新西兰用澳洲链接
        
        // 其他语言保持不变
        'es': 'https://www.xibanya.com',
        'de': 'https://www.amazon.de/dp/B0DZXKLNVL',
        'fr': 'https://www.faguo.com',
        'it': 'https://www.yidaliyu.com',
        
        // 默认英语
        'en': 'https://www.amazon.com/dp/B0DR7TZGV8'
    };

    // 2. 获取用户国家的函数
    async function getUserCountry() {
        try {
            const response = await fetch('https://ipapi.co/json/');
            const data = await response.json();
            return data.country_code;
        } catch (error) {
            console.log('IP检测失败，使用默认美国链接');
            return 'US';
        }
    }

    // 3. 更新的购买按钮链接函数（同时更新主页和模态框按钮）
    async function updateBuyButtonLink() {
        const currentBuyButton = document.querySelector('.aphm1-button-secondary');
        const currentModalBuyButton = document.querySelector('.aphm1-modal-buy');

        if (!currentBuyButton) {
            console.error("Buy button with class '.aphm1-button-secondary' not found.");
            return;
        }

        const currentLang = document.documentElement.lang.split('-')[0];
        let targetLink;

        // 先设置默认链接，避免显示#
        const defaultLink = buyLinks['en'] || 'https://amazon.com/knka-dehumidifier';
        currentBuyButton.href = defaultLink;
        if (currentModalBuyButton) {
            currentModalBuyButton.href = defaultLink;
        }
        console.log('设置默认链接:', defaultLink);

        if (currentLang === 'en') {
            // 英语时进一步检测国家
            try {
                const country = await getUserCountry();
                const langCountry = `en-${country}`;
                targetLink = buyLinks[langCountry] || buyLinks['en'];
                console.log(`English detected. Country: ${country}, Using link: ${targetLink}`);
            } catch (error) {
                console.log('获取国家信息失败，使用默认英语链接');
                targetLink = buyLinks['en'];
            }
        } else {
            // 非英语直接使用语言代码
            targetLink = buyLinks[currentLang] || buyLinks['en'];
            console.log(`Language: ${currentLang}, Using link: ${targetLink}`);
        }

        // 同时更新两个按钮的链接
        currentBuyButton.href = targetLink;
        if (currentModalBuyButton) {
            currentModalBuyButton.href = targetLink;
            console.log('模态框按钮链接也已更新:', currentModalBuyButton.href);
        }
        console.log('主页按钮最终链接:', currentBuyButton.href);
    }

    // 4. 使用 MutationObserver 监视 <html> 标签的 lang 属性变化
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'lang') {
                updateBuyButtonLink(); // 当 lang 属性变化时，调用更新函数
            }
        });
    });

    // 5. 配置并启动观察器，让它开始监视
    observer.observe(document.documentElement, {
        attributes: true // 我们只关心属性的变化
    });

    // 6. 页面加载完成后，立即执行一次，以正确设置初始的购买链接
    updateBuyButtonLink();
  }

  // 多种方式确保代码执行，适配Elementor环境
  document.addEventListener('DOMContentLoaded', initializeElements);
  window.addEventListener('load', initializeElements);

  // 如果页面已经加载完成，立即执行
  if (document.readyState === 'complete' || document.readyState === 'interactive') {
    setTimeout(initializeElements, 100);
  }
</script>
