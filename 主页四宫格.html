<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>KNKA Products Grid</title>
<style>
/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', Helvetica, Arial, sans-serif;
  color: #1d1d1f;
  line-height: 1.47059;
  overflow-x: hidden; /* 防止水平滚动 */
}

/* 网格容器 - 固定比例设置 */
.aphm4-grid-container {
  width: 100%;
  max-width: 100%;
  padding: 10px; /* 页面边缘的内边距 */
  background-color: #fff; /* 网格背景色 */
  box-sizing: border-box; /* 确保内边距包含在总高度内 */
  position: relative;
}

/* 网格容器 - 使用grid布局 */
.aphm4-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px; /* 更优雅的间距 */
  width: 100%; /* 始终占据100%宽度 */
  box-sizing: border-box;
  /* 不再需要JavaScript设置高度 */
}

/* 网格项 - 使用aspect-ratio保持4:3比例 */
.aphm4-grid-item {
  position: relative;
  aspect-ratio: 4/3; /* 设置4:3的宽高比 */
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 45px 30px 30px; /* 增加左右内边距，确保图片不会溢出 */
  overflow: hidden; /* 防止内容溢出 */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.02); /* 轻微阴影增加层次感 */
  transition: transform 0.3s ease; /* 平滑过渡效果 */
  justify-content: flex-start; /* 所有格子内容从顶部开始 */
}

/* 轻微的悬停效果 */
.aphm4-grid-item:hover {
  transform: translateY(-2px);
}

/* 学生优惠区块 */
.aphm4-college {
  background-color: #f5f5f7;
}

/* iPad Air区块 */
.aphm4-ipad {
  /*background-image: linear-gradient(to bottom, #addcee, #f2f6f7);*/
  background-color: #e6f2ff;
}

/* AirPods Pro区块 */
.aphm4-airpods {
  background-image: linear-gradient(to bottom, #addcee, #f2f6f7);
  /* 移除颜色覆盖 */
  /* color: #fff; */
}

/* MacBook Pro区块 */
.aphm4-macbook {
  background-color: #f5f5f7;
  /* 移除颜色覆盖 */
  /* color: #fff; */
}

/* 标题样式 */
.aphm4-title {
  font-size: 40px;
  line-height: 1.1;
  font-weight: 600;
  letter-spacing: -0.002em;
  margin-bottom: 8px;
  text-align: center;
}

.aphm4-title .aphm4-light {
  font-weight: 400;
}

/* 副标题样式 */
.aphm4-subtitle {
  font-size: 21px;
  line-height: 1.2381;
  font-weight: 400;
  letter-spacing: 0.016em;
  margin-bottom: 10px; /* 减少底部间距 */
  text-align: center;
  color: #1d1d1f; /* 确保所有副标题颜色一致 */
}

/* 按钮容器 - 增加底部边距防止与图片重叠 */
.aphm4-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 10px; /* 减少顶部间距 */
  margin-bottom: 15px; /* 从原来的30px减少到15px */
  z-index: 5; /* 确保按钮在图片上方 */
  position: relative; /* 添加相对定位 */
}

/* 恢复上一版本的按钮样式 */
.aphm4-button {
  background: #0071e3;
  color: #fff;
  border-radius: 980px;
  font-size: 14px;
  line-height: 1.4285;
  font-weight: 400;
  letter-spacing: -0.016em;
  padding: 8px 16px;
  text-decoration: none;
  text-align: center;
  min-width: 28px;
  transition: all 0.2s ease; /* 按钮过渡效果 */
  cursor: pointer;
  border: none; /* 确保没有边框 */
  display: inline-flex; /* 使用inline-flex确保内容垂直居中 */
  align-items: center; /* 垂直居中文本 */
  justify-content: center; /* 水平居中文本 */
  height: 40px; /* 固定高度确保两个按钮一致 */
}

/* 调整悬停效果为蓝色并移除边框 */
.aphm4-button:hover,
.aphm4-button:focus,
.aphm4-button:active {
  background-color: #0077ed; /* 更亮的蓝色 */
  transform: scale(1.02);
  outline: none; /* 移除焦点时的轮廓 */
  box-shadow: none; /* 移除可能的阴影 */
  border: none; /* 确保没有边框 */
}

.aphm4-button.aphm4-secondary {
  background: rgba(0, 0, 0, 0.08);
  color: #1d1d1f;
}

/* 产品图片容器 - 修改为更灵活的尺寸控制 */
.aphm4-image-container {
  width: 90%; /* 设置容器宽度，留出边缘空间 */
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden; /* 防止图片溢出 */
  margin-top: -20px; /* 减小顶部负边距 */
  margin-bottom: 20px; /* 添加底部间距，与底部文字保持距离 */
  flex: 1; /* 让图片容器占据剩余空间 */
  /* 使用比例而不是固定高度 */
  aspect-ratio: 16/9;
}

/* 产品图片基础样式 - 修改为更好的适应性 */
.aphm4-product-image {
  max-width: 100%; /* 最大宽度为100% */
  max-height: 100%; /* 最大高度为100% */
  width: auto; /* 宽度自动 */
  height: auto; /* 高度自动 */
  object-fit: contain; /* 保持图片比例，确保整个图片可见 */
  object-position: center center;
  transform: none; /* 移除缩放变换 */
}

/* 底部文本 - 添加渐变色效果 */
.aphm4-footer-text {
  font-size: 18px;
  line-height: 1.4285;
  font-weight: 500; /* 增加字重使渐变色更明显 */
  letter-spacing: -0.016em;
  margin-top: auto; /* 将文本推到底部 */
  margin-bottom: 25px; /* 增加底部间距，与网格边缘保持距离 */
  padding: 0 20px; /* 添加左右内边距，与网格边缘保持距离 */
  background: linear-gradient(90deg, #2784e3 0%, #ff5cef 50%, #ff8695 100%);
  background-size: 200% auto;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradient 3s linear infinite;
  text-align: center;
  width: 100%;
}

/* 为蓝粉渐变添加特殊类 */
.aphm4-footer-text.blue-pink {
  background: linear-gradient(90deg, #4285F4 0%, #EA4C89 100%);
  background-size: 200% auto;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 渐变动画效果 */
@keyframes gradient {
  0% {
    background-position: 0% center;
  }
  50% {
    background-position: 100% center;
  }
  100% {
    background-position: 0% center;
  }
}

/* 毛玻璃背景 */
.aphm4-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  z-index: 999;
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* 弹出卡片样式 - 优化后的样式 */
.aphm4-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.95);
  width: 90%;
  max-width: 1000px;
  max-height: 85vh;
  background-color: #fff;
  z-index: 1000;
  display: none;
  opacity: 0;
  transition: all 0.3s ease;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  border-radius: 16px;
  overflow: hidden;
}

/* 弹出卡片内容 */
.aphm4-modal-content {
  padding: 24px;
  height: 100%;
  max-height: 85vh;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  display: flex;
  flex-direction: column;
}

/* 隐藏WebKit浏览器的滚动条 */
.aphm4-modal-content::-webkit-scrollbar {
  display: none;
}

/* 产品类别标题 */
.aphm4-modal-category {
  font-size: 14px;
  line-height: 1.42859;
  font-weight: 400;
  letter-spacing: -0.016em;
  color: #86868b;
  margin-bottom: 12px;
  margin-left: 4px;
}

/* 弹出卡片主体 - 左右布局 */
.aphm4-modal-body {
  display: flex;
  flex-direction: row;
  gap: 32px;
  width: 100%;
  min-height: 380px;
}

/* 左侧产品图片区域 */
.aphm4-modal-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0;
}

/* 产品图片容器 */
.aphm4-modal-image {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  border-radius: 8px;
}

.aphm4-modal-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* 右侧产品信息区域 */
.aphm4-modal-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 4px 0;
}

/* 产品标题 */
.aphm4-modal-product-title {
  font-size: 36px;
  line-height: 1.1;
  font-weight: 600;
  letter-spacing: -0.002em;
  color: #1d1d1f;
  margin: 0 0 16px 0;
}

/* 价格和购买按钮容器 */
.aphm4-modal-price-buy-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

/* 产品价格 */
.aphm4-modal-price {
  font-size: 15px;
  line-height: 1.4;
  font-weight: 400;
  color: #1d1d1f;
  margin: 0;
  max-width: 80%;
}

/* 弹出卡片内的购买按钮 */
.aphm4-modal-buy {
  background: #0071e3;
  color: #fff;
  border-radius: 980px;
  font-size: 15px;
  line-height: 1.47059;
  font-weight: 500;
  padding: 6px 16px;
  text-decoration: none;
  text-align: center;
  display: inline-block;
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
  width: 80px;
}

.aphm4-modal-buy:hover {
  background-color: #0077ed;
}

/* 产品特性列表 */
.aphm4-modal-features {
  display: flex;
  flex-direction: column;
  gap: 0;
}

/* 产品特性项 */
.aphm4-feature-item {
  display: flex;
  gap: 12px;
  padding: 12px 0;
  border-top: 1px solid #d2d2d7;
}

.aphm4-feature-item:first-child {
  border-top: none;
  padding-top: 0;
}

/* 特性图标 */
.aphm4-feature-icon {
  width: 32px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

.aphm4-feature-icon svg {
  width: 24px;
  height: 24px;
}

/* 特性文本 */
.aphm4-feature-content {
  flex: 1;
}

.aphm4-feature-title {
  font-size: 15px;
  line-height: 1.4;
  font-weight: 600;
  margin-bottom: 4px;
  color: #1d1d1f;
}

.aphm4-feature-text {
  font-size: 15px;
  line-height: 1.4;
  color: #1d1d1f;
}

/* 上标样式 */
.aphm4-superscript {
  font-size: 0.6em;
  vertical-align: super;
}

/* 关闭按钮 */
.aphm4-modal-close {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: none;
  z-index: 10;
  transition: background-color 0.2s ease;
  padding: 0;
  margin: 0;
}

.aphm4-modal-close:hover {
  background-color: #000;
}

.aphm4-modal-close svg {
  width: 10px;
  height: 10px;
  display: block;
}

/* 显示弹出卡片和背景 */
.aphm4-modal.active {
  display: block;
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

.aphm4-modal-backdrop.active {
  display: block;
  opacity: 1;
}

/* 响应式调整 */
@media only screen and (max-width: 1068px) {
  .aphm4-title {
    font-size: 32px;
  }
  
  .aphm4-subtitle {
    font-size: 19px;
  }
  
  .aphm4-grid-item {
    padding: 35px 25px 25px; /* 减少内边距 */
  }
  
  .aphm4-modal-product-title {
    font-size: 28px;
  }
  
  .aphm4-modal-body {
    gap: 24px;
  }
  
  .aphm4-image-container {
    margin-top: -20px; /* 中等屏幕上调整顶部负边距 */
    width: 95%; /* 在中等屏幕上增加容器宽度 */
  }
  
  .aphm4-footer-text {
    margin-bottom: 20px; /* 调整底部间距 */
  }
  
  /* 确保按钮和图片不重叠 */
  .aphm4-buttons {
    margin-bottom: 12px; /* 中等屏幕上减少底部边距 */
  }
}

@media only screen and (max-width: 734px) {
  .aphm4-grid {
    grid-template-columns: 1fr;
    gap: 8px; /* 移动端更小的间距 */
  }
  
  .aphm4-title {
    font-size: 28px;
  }
  
  .aphm4-subtitle {
    font-size: 17px;
  }
  
  .aphm4-grid-item {
    padding: 30px 20px 20px; /* 移动端更小的内边距 */
  }
  
  .aphm4-modal-product-title {
    font-size: 24px;
    margin-bottom: 12px;
  }
  
  .aphm4-modal-content {
    padding: 20px;
  }
  
  /* 移动端弹窗布局调整为垂直方向 */
  .aphm4-modal-body {
    flex-direction: column;
    gap: 16px;
    min-height: auto;
  }
  
  /* 移动端下图片区域添加底部间距 */
  .aphm4-modal-left {
    margin-bottom: 0;
    max-height: 200px;
  }
  
  .aphm4-feature-item {
    padding: 10px 0;
  }
  
  .aphm4-feature-icon {
    width: 28px;
    height: 28px;
  }
  
  .aphm4-feature-icon svg {
    width: 20px;
    height: 20px;
  }
  
  .aphm4-feature-title {
    font-size: 14px;
  }
  
  .aphm4-feature-text {
    font-size: 14px;
  }
  
  /* 移动端图片位置调整 */
  .aphm4-image-container {
    margin-top: -15px; /* 移动端调整顶部负边距 */
    width: 90%;
    margin-bottom: 15px; /* 减少底部间距 */
  }
  
  .aphm4-footer-text {
    margin-bottom: 15px; /* 移动端减少底部间距 */
    font-size: 13px; /* 移动端减小字体 */
  }
  
  /* 确保按钮和图片不重叠 */
  .aphm4-buttons {
    margin-bottom: 10px; /* 移动端减少底部边距 */
  }
}

/* 小屏幕特殊处理 */
@media only screen and (max-width: 480px) {
  .aphm4-buttons {
    margin-bottom: 20px; /* 小屏幕上增加底部边距 */
  }
}

/* 新的按钮样式 - 基于参考代码 */
.aphm4-button {
  display: inline-block;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 16px;
  font-weight: 400;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  height: auto; /* 移除固定高度 */
}

.aphm4-button-primary {
  background-color: #0071e3;
  color: white;
}

.aphm4-button-primary:hover {
  background-color: #0062c3;
  transform: scale(1.05);
}

.aphm4-button-secondary {
  background-color: white;
  color: #0071e3;
  border: 1px solid #0071e3;
}

.aphm4-button-secondary:hover {
  background-color: rgba(0, 113, 227, 0.1);
  transform: scale(1.05);
}

/* 覆盖原有的按钮悬停效果 */
.aphm4-button:hover,
.aphm4-button:focus,
.aphm4-button:active {
  outline: none;
  box-shadow: none;
  border: none;
}

.aphm4-button-secondary:hover,
.aphm4-button-secondary:focus,
.aphm4-button-secondary:active {
  border: 1px solid #0071e3;
}

/* 弹出卡片内的购买按钮 - 更新样式 */
.aphm4-modal-buy {
  background: #0071e3;
  color: #fff;
  border-radius: 20px;
  font-size: 15px;
  line-height: 1.47059;
  font-weight: 500;
  padding: 8px 16px;
  text-decoration: none;
  text-align: center;
  display: inline-block;
  cursor: pointer;
  border: none;
  transition: all 0.3s ease;
  min-width: 100px;
}

.aphm4-modal-buy:hover {
  background-color: #0062c3;
  transform: scale(1.05);
}
</style>
</head>
<body>
<div class="aphm4-grid-container">
  <div class="aphm4-grid">
    <!-- aph3000 -->
    <div class="aphm4-grid-item aphm4-college">
      <h2 class="aphm4-title">Air Purifier</h2>
      <p class="aphm4-subtitle">APH3000</p>
      <div class="aphm4-buttons">
        <a href="#" class="aphm4-button aphm4-button-primary" data-modal="college-modal">Learn more</a>
        <a href="#" class="aphm4-button aphm4-button-secondary">Shop Here</a>
      </div>
      <div class="aphm4-image-container">
        <img src="https://knkalife.com/wp-content/uploads/2025/05/透明底-1.png" class="aphm4-product-image">
      </div>
      <p class="aphm4-footer-text blue-pink">Keep Natural Keep Advancing</p>
    </div>
    
    <!-- pd22sc区块 -->
    <div class="aphm4-grid-item aphm4-ipad">
      <h2 class="aphm4-title">Dehumidifier <span class="aphm4-light"></span></h2>
      <p class="aphm4-subtitle">PD22SC</p>
      <div class="aphm4-buttons">
        <a href="#" class="aphm4-button aphm4-button-primary" data-modal="ipad-modal">Learn more</a>
        <a href="#" class="aphm4-button aphm4-button-secondary">Shop Here</a>
      </div>
      <div class="aphm4-image-container">
        <img src="https://knkalife.com/wp-content/uploads/2025/05/透明底2-1.png" alt="iPad Air" class="aphm4-product-image">
      </div>
      <p class="aphm4-footer-text blue-pink">Keep Natural Keep Advancing</p>
    </div>
    
    <!-- d032区块 - 移除了aphm4-dark类 -->
    <div class="aphm4-grid-item aphm4-airpods">
      <h2 class="aphm4-title">Dehumidifier</h2>
      <p class="aphm4-subtitle">D032<sup></sup></p>
      <div class="aphm4-buttons">
        <a href="#" class="aphm4-button aphm4-button-primary" data-modal="airpods-modal">Learn more</a>
        <a href="#" class="aphm4-button aphm4-button-secondary">Shop Here</a>
      </div>
      <div class="aphm4-image-container">
        <img src="https://knkalife.com/wp-content/uploads/2025/05/透明底1-1.png" alt="AirPods Pro 2" class="aphm4-product-image">
      </div>
      <p class="aphm4-footer-text blue-pink">Keep Natural Keep Advancing</p>
    </div>
    
    <!-- AP2000WF区块 - 移除了aphm4-dark类 -->
    <div class="aphm4-grid-item aphm4-macbook">
      <h2 class="aphm4-title">Air Purifier</h2>
      <p class="aphm4-subtitle">AP2000WF</p>
      <div class="aphm4-buttons">
        <a href="#" class="aphm4-button aphm4-button-primary" data-modal="macbook-modal">Learn more</a>
        <a href="#" class="aphm4-button aphm4-button-secondary">Shop Here</a>
      </div>
      <div class="aphm4-image-container">
        <img src="https://knkalife.com/wp-content/uploads/2025/05/4-e1747356542606.png" alt="MacBook Pro" class="aphm4-product-image">
      </div>
      <p class="aphm4-footer-text blue-pink">Keep Natural Keep Advancing</p>
    </div>
  </div>
</div>

<!-- 毛玻璃背景 -->
<div class="aphm4-modal-backdrop" id="modal-backdrop"></div>

<!-- Air Purifier APH3000 弹出卡片 - 优化后的样式 -->
<div class="aphm4-modal" id="college-modal">
  <div class="aphm4-modal-content">
    <p class="aphm4-modal-category">Air Purifier</p>
    
    <div class="aphm4-modal-body">
      <div class="aphm4-modal-left">
        <div class="aphm4-modal-image">
          <img src="https://knkalife.com/wp-content/uploads/2025/05/主图3-1.jpg" alt="Air Purifier APH3000">
        </div>
      </div>
      
      <div class="aphm4-modal-right">
        <h2 class="aphm4-modal-product-title">APH3000</h2>
        <div class="aphm4-modal-price-buy-container">
          <p class="aphm4-modal-price"></p>
          <a href="#" class="aphm4-modal-buy">Buy</a>
        </div>
        
        <div class="aphm4-modal-features">
          <div class="aphm4-feature-item">
            <div class="aphm4-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="#1d1d1f" stroke-width="2"/>
                <path d="M12 6V12L16 14" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </div>
            <div class="aphm4-feature-content">
              <p class="aphm4-feature-text"><strong>Turbo Mode, Pure Air, Fast.</strong><br>Boost purification speed by over 30%. Includes child lock, aroma sponge, and sleek, pet-friendly design.</p>
            </div>
          </div>
          
          <div class="aphm4-feature-item">
            <div class="aphm4-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#1d1d1f" stroke-width="2"/>
                <path d="M12 7V12L15 15" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="aphm4-feature-content">
              <p class="aphm4-feature-text"><strong>Pure Air, Wide Reach.</strong><br>True HEPA cleans spaces up to 2,325 sq. ft. Powerful circulation ensures fresh, healthy air wherever you need it.</p>
            </div>
          </div>
          
          <div class="aphm4-feature-item">
            <div class="aphm4-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 12L11 14L15 10" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#1d1d1f" stroke-width="2"/>
              </svg>
            </div>
            <div class="aphm4-feature-content">
              <p class="aphm4-feature-text"><strong>Triple Filtration, Lasting Clean.</strong><br>Washable pre-filter protects the H13 HEPA, while activated carbon traps 99.9% of particles down to 0.3 microns.</p>
            </div>
          </div>
          
          <div class="aphm4-feature-item">
            <div class="aphm4-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 18C15.3137 18 18 15.3137 18 12C18 8.68629 15.3137 6 12 6C8.68629 6 6 8.68629 6 12C6 15.3137 8.68629 18 12 18Z" stroke="#1d1d1f" stroke-width="2"/>
                <path d="M12 2V4" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round"/>
                <path d="M12 20V22" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round"/>
                <path d="M4 12H2" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round"/>
                <path d="M22 12H20" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </div>
            <div class="aphm4-feature-content">
              <p class="aphm4-feature-text"><strong>Smart Sensing, Automatic Precision.</strong><br>Continuous air quality monitoring with real-time LED updates. Auto Mode adjusts fan speed for optimal clean and efficiency.</p>
            </div>
          </div>

          <div class="aphm4-feature-item">
            <div class="aphm4-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 18V12C3 9.61305 3.94821 7.32387 5.63604 5.63604C7.32387 3.94821 9.61305 3 12 3C14.3869 3 16.6761 3.94821 18.364 5.63604C20.0518 7.32387 21 9.61305 21 12V18" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M21 19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H18C17.4696 21 16.9609 20.7893 16.5858 20.4142C16.2107 20.0391 16 19.5304 16 19V16C16 15.4696 16.2107 14.9609 16.5858 14.5858C16.9609 14.2107 17.4696 14 18 14H21V19ZM3 19C3 19.5304 3.21071 20.0391 3.58579 20.4142C3.96086 20.7893 4.46957 21 5 21H6C6.53043 21 7.03914 20.7893 7.41421 20.4142C7.78929 20.0391 8 19.5304 8 19V16C8 15.4696 7.78929 14.9609 7.41421 14.5858C7.03914 14.2107 6.53043 14 6 14H3V19Z" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="aphm4-feature-content">
              <p class="aphm4-feature-text"><strong>Whisper-Quiet, Restful Sleep.</strong><br>Operates at 24dB with adjustable lights, ensuring peaceful nights and continuous purification.</p>
            </div>
          </div>
          
          <div class="aphm4-feature-item">
            <div class="aphm4-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 18C15.3137 18 18 15.3137 18 12C18 8.68629 15.3137 6 12 6C8.68629 6 6 8.68629 6 12C6 15.3137 8.68629 18 12 18Z" stroke="#1d1d1f" stroke-width="2"/>
                <path d="M12 8V12L15 15" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M19 12H21" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M3 12H5" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 19V21" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 3V5" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="aphm4-feature-content">
              <p class="aphm4-feature-text"><strong>Tailored Airflow, Intelligent Timing.</strong><br>Three fan speeds plus Auto Mode that adapts to air quality. Set the timer for up to 12 hours for effortless control.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <button class="aphm4-modal-close" aria-label="Close">
      <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1 1L11 11M11 1L1 11" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
      </svg>
    </button>
  </div>
</div>

<!-- Dehumidifier PD22SC 弹出卡片 - 优化后的样式 -->
<div class="aphm4-modal" id="ipad-modal">
  <div class="aphm4-modal-content">
    <p class="aphm4-modal-category">Dehumidifier</p>
    
    <div class="aphm4-modal-body">
      <div class="aphm4-modal-left">
        <div class="aphm4-modal-image">
          <img src="https://knkalife.com/wp-content/uploads/2025/05/主图1-1.jpg" alt="Dehumidifier PD22SC">
        </div>
      </div>
      
      <div class="aphm4-modal-right">
        <h2 class="aphm4-modal-product-title">PD22SC</h2>
        <div class="aphm4-modal-price-buy-container">
          <p class="aphm4-modal-price">Let the unit stand for 24 hours after delivery to ensure optimal performance and long-term reliability.</p>
          <a href="#" class="aphm4-modal-buy">Buy</a>
        </div>
        
        <div class="aphm4-modal-features">
          <div class="aphm4-feature-item">
            <div class="aphm4-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20 7L12 3L4 7" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M20 7V17L12 21L4 17V7" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 11L20 7" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 11L4 7" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 11V21" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="aphm4-feature-content">
              <p class="aphm4-feature-text"><strong>Smart Control. Seamless Comfort.</strong><br>Set humidity, fan speed, timer, and more—all with a tap. It auto-adjusts, so you don't have to.</p>
            </div>
          </div>
          
          <div class="aphm4-feature-item">
            <div class="aphm4-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M6 7H18C19.1046 7 20 7.89543 20 9V17C20 18.1046 19.1046 19 18 19H6C4.89543 19 4 18.1046 4 17V9C4 7.89543 4.89543 7 6 7Z" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M9 13H15" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M15 7V5C15 3.89543 14.1046 3 13 3H11C9.89543 3 9 3.89543 9 5V7" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="aphm4-feature-content">
              <p class="aphm4-feature-text"><strong>Powerful, Precise. Built for 2,500 Sq. Ft.</strong><br>Removes up to 34 pints daily to keep your entire home comfortably dry—quietly and efficiently.</p>
            </div>
          </div>
          
          <div class="aphm4-feature-item">
            <div class="aphm4-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 4C14.7614 4 17 6.23858 17 9C17 11.7614 14.7614 14 12 14C9.23858 14 7 11.7614 7 9C7 6.23858 9.23858 4 12 4Z" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 14V20" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M9 17H15" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="aphm4-feature-content">
              <p class="aphm4-feature-text"><strong>Three Modes, Total Control.</strong><br>DEHU for custom humidity. DRY for faster clothes drying. CONT for nonstop moisture removal.</p>
            </div>
          </div>
          
          <div class="aphm4-feature-item">
            <div class="aphm4-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 12V19" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 5V5.01" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="aphm4-feature-content">
              <p class="aphm4-feature-text"><strong>Dual Drainage, Zero Interruptions.</strong><br>Auto-stop with full tank. Or go hands-free with continuous drainage via the included hose.</p>
            </div>
          </div>
          
          <div class="aphm4-feature-item">
            <div class="aphm4-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 18C15.3137 18 18 15.3137 18 12C18 8.68629 15.3137 6 12 6C8.68629 6 6 8.68629 6 12C6 15.3137 8.68629 18 12 18Z" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 2V4" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 20V22" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M4.92993 4.93005L6.33993 6.34005" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M17.6599 17.66L19.0699 19.07" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M2 12H4" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M20 12H22" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M4.92993 19.07L6.33993 17.66" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M17.6599 6.34005L19.0699 4.93005" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="aphm4-feature-content">
              <p class="aphm4-feature-text"><strong>Whisper-Quiet, Always Aware.</strong><br>Runs as quiet as a whisper. The LED ring subtly shifts color to show real-time humidity—even when off.</p>
            </div>
          </div>

          <div class="aphm4-feature-item">
            <div class="aphm4-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M15 10L19 14L15 18" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M19 14H9" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="aphm4-feature-content">
              <p class="aphm4-feature-text"><strong>Designed to Move, Built to Last.</strong><br>360° wheels, soft-touch handle, and a washable filter—every detail made for effortless living.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <button class="aphm4-modal-close" aria-label="Close">
      <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1 1L11 11M11 1L1 11" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
      </svg>
    </button>
  </div>
</div>

<!-- Dehumidifier D032 弹出卡片 - 优化后的样式 -->
<div class="aphm4-modal" id="airpods-modal">
  <div class="aphm4-modal-content">
    <p class="aphm4-modal-category">Dehumidifier</p>
    
    <div class="aphm4-modal-body">
      <div class="aphm4-modal-left">
        <div class="aphm4-modal-image">
          <img src="https://knkalife.com/wp-content/uploads/2025/05/主图.jpg" alt="Dehumidifier D032">
        </div>
      </div>
      
      <div class="aphm4-modal-right">
        <h2 class="aphm4-modal-product-title">D032</h2>
        <div class="aphm4-modal-price-buy-container">
          <p class="aphm4-modal-price"></p>
          <a href="#" class="aphm4-modal-buy">Buy</a>
        </div>
        
        <div class="aphm4-modal-features">
          <div class="aphm4-feature-item">
            <div class="aphm4-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 18V12C3 9.61305 3.94821 7.32387 5.63604 5.63604C7.32387 3.94821 9.61305 3 12 3C14.3869 3 16.6761 3.94821 18.364 5.63604C20.0518 7.32387 21 9.61305 21 12V18" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M21 19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H18C17.4696 21 16.9609 20.7893 16.5858 20.4142C16.2107 20.0391 16 19.5304 16 19V16C16 15.4696 16.2107 14.9609 16.5858 14.5858C16.9609 14.2107 17.4696 14 18 14H21V19ZM3 19C3 19.5304 3.21071 20.0391 3.58579 20.4142C3.96086 20.7893 4.46957 21 5 21H6C6.53043 21 7.03914 20.7893 7.41421 20.4142C7.78929 20.0391 8 19.5304 8 19V16C8 15.4696 7.78929 14.9609 7.41421 14.5858C7.03914 14.2107 6.53043 14 6 14H3V19Z" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="aphm4-feature-content">
              <p class="aphm4-feature-text"><strong>Compact Power, Big Coverage.</strong><br>Up to 19 pints per day for spaces up to 1,300 sq. ft. Precise humidity control. Whisper-quiet performance.</p>
            </div>
          </div>
          
          <div class="aphm4-feature-item">
            <div class="aphm4-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M18.7273 14.7273C18.6063 15.0909 18.6273 15.4818 18.7891 15.8182C18.9509 16.1545 19.2418 16.4091 19.6 16.5455L19.8364 16.6364C20.2691 16.8 20.6145 17.1455 20.7782 17.5782C20.9418 18.0109 20.9055 18.4873 20.6782 18.8909C20.4509 19.2945 20.0582 19.5818 19.6 19.6727L19.3455 19.7273C18.9873 19.8182 18.6782 20.0727 18.5164 20.4091C18.3545 20.7455 18.3336 21.1364 18.4545 21.5L18.5091 21.7545C18.6364 22.2127 18.5491 22.6982 18.2727 23.0727C17.9964 23.4473 17.5673 23.6727 17.1091 23.6727H16.8909C16.4327 23.6727 16.0036 23.4473 15.7273 23.0727C15.4509 22.6982 15.3636 22.2127 15.4909 21.7545L15.5455 21.5C15.6664 21.1364 15.6455 20.7455 15.4836 20.4091C15.3218 20.0727 15.0127 19.8182 14.6545 19.7273L14.4 19.6727C13.9418 19.5818 13.5491 19.2945 13.3218 18.8909C13.0945 18.4873 13.0582 18.0109 13.2218 17.5782C13.3855 17.1455 13.7309 16.8 14.1636 16.6364L14.4 16.5455C14.7582 16.4091 15.0491 16.1545 15.2109 15.8182C15.3727 15.4818 15.3936 15.0909 15.2727 14.7273L15.2182 14.4727C15.0909 14.0145 15.1782 13.5291 15.4545 13.1545C15.7309 12.78 16.16 12.5545 16.6182 12.5545H16.8364C17.2945 12.5545 17.7236 12.78 18 13.1545C18.2764 13.5291 18.3636 14.0145 18.2364 14.4727L18.1818 14.7273Z" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M8.72727 14.7273C8.60636 15.0909 8.62727 15.4818 8.78909 15.8182C8.95091 16.1545 9.24182 16.4091 9.6 16.5455L9.83636 16.6364C10.2691 16.8 10.6145 17.1455 10.7782 17.5782C10.9418 18.0109 10.9055 18.4873 10.6782 18.8909C10.4509 19.2945 10.0582 19.5818 9.6 19.6727L9.34545 19.7273C8.98727 19.8182 8.67818 20.0727 8.51636 20.4091C8.35455 20.7455 8.33364 21.1364 8.45455 21.5L8.50909 21.7545C8.63636 22.2127 8.54909 22.6982 8.27273 23.0727C7.99636 23.4473 7.56727 23.6727 7.10909 23.6727H6.89091C6.43273 23.6727 6.00364 23.4473 5.72727 23.0727C5.45091 22.6982 5.36364 22.2127 5.49091 21.7545L5.54545 21.5C5.66636 21.1364 5.64545 20.7455 5.48364 20.4091C5.32182 20.0727 5.01273 19.8182 4.65455 19.7273L4.4 19.6727C3.94182 19.5818 3.54909 19.2945 3.32182 18.8909C3.09455 18.4873 3.05818 18.0109 3.22182 17.5782C3.38545 17.1455 3.73091 16.8 4.16364 16.6364L4.4 16.5455C4.75818 16.4091 5.04909 16.1545 5.21091 15.8182C5.37273 15.4818 5.39364 15.0909 5.27273 14.7273L5.21818 14.4727C5.09091 14.0145 5.17818 13.5291 5.45455 13.1545C5.73091 12.78 6.16 12.5545 6.61818 12.5545H6.83636C7.29455 12.5545 7.72364 12.78 8 13.1545C8.27636 13.5291 8.36364 14.0145 8.23636 14.4727L8.18182 14.7273H8.72727Z" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 12V3" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M15.3891 5.5L8.61091 8.5" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M8.61091 5.5L15.3891 8.5" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="aphm4-feature-content">
              <p class="aphm4-feature-text"><strong>Touch to Control, Built to Think.</strong><br>Adjust humidity, modes, and more with a smart touch panel. Real-time display. 24-hour timer. Total precision.</p>
            </div>
          </div>
          
          <div class="aphm4-feature-item">
            <div class="aphm4-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 18C15.3137 18 18 15.3137 18 12C18 8.68629 15.3137 6 12 6C8.68629 6 6 8.68629 6 12C6 15.3137 8.68629 18 12 18Z" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 2V4" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 20V22" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M4.92993 4.93005L6.33993 6.34005" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M17.6599 17.66L19.0699 19.07" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M2 12H4" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M20 12H22" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M4.92993 19.07L6.33993 17.66" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M17.6599 6.34005L19.0699 4.93005" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="aphm4-feature-content">
              <p class="aphm4-feature-text"><strong>Ultra-Quiet, Restful Nights.</strong><br>Operates at just 40dB—quieter than a whisper. Sleep mode dims lights and lowers noise for uninterrupted rest.</p>
            </div>
          </div>
          
          <div class="aphm4-feature-item">
            <div class="aphm4-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M9 22V12H15V22" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="aphm4-feature-content">
              <p class="aphm4-feature-text"><strong>Portable, Precise, Effortless.</strong><br>Built-in handle, 360° wheels, washable filter, and a humidity-responsive LED for easy control and fresh air anywhere.</p>
            </div>
          </div>
          
          <div class="aphm4-feature-item">
            <div class="aphm4-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 18C15.3137 18 18 15.3137 18 12C18 8.68629 15.3137 6 12 6C8.68629 6 6 8.68629 6 12C6 15.3137 8.68629 18 12 18Z" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 2V4" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 20V22" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M4.92993 4.93005L6.33993 6.34005" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M17.6599 17.66L19.0699 19.07" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M2 12H4" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M20 12H22" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M4.92993 19.07L6.33993 17.66" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M17.6599 6.34005L19.0699 4.93005" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="aphm4-feature-content">
              <p class="aphm4-feature-text"><strong>Dual Drainage, Clear Control.</strong><br>Auto shut-off with visible water tank and indicator. Or connect the included hose for hassle-free continuous drainage.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <button class="aphm4-modal-close" aria-label="Close">
      <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1 1L11 11M11 1L1 11" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
      </svg>
    </button>
  </div>
</div>

<!-- Air Purifier AP2000WF 弹出卡片 - 优化后的样式 -->
<div class="aphm4-modal" id="macbook-modal">
  <div class="aphm4-modal-content">
    <p class="aphm4-modal-category">Air Purifier</p>
    
    <div class="aphm4-modal-body">
      <div class="aphm4-modal-left">
        <div class="aphm4-modal-image">
          <img src="https://knkalife.com/wp-content/uploads/2025/05/主图2.jpg" alt="Air Purifier AP2000WF">
        </div>
      </div>
      
      <div class="aphm4-modal-right">
        <h2 class="aphm4-modal-product-title">AP2000WF</h2>
        <div class="aphm4-modal-price-buy-container">
          <p class="aphm4-modal-price"></p>
          <a href="#" class="aphm4-modal-buy">Buy</a>
        </div>
        
        <div class="aphm4-modal-features">
          <div class="aphm4-feature-item">
            <div class="aphm4-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20 16V7C20 5.93913 19.5786 4.92172 18.8284 4.17157C18.0783 3.42143 17.0609 3 16 3H8C6.93913 3 5.92172 3.42143 5.17157 4.17157C4.42143 4.92172 4 5.93913 4 7V16C4 17.0609 4.42143 18.0783 5.17157 18.8284C5.92172 19.5786 6.93913 20 8 20H16C17.0609 20 18.0783 19.5786 18.8284 18.8284C19.5786 18.0783 20 17.0609 20 16Z" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 17C14.2091 17 16 15.2091 16 13C16 10.7909 14.2091 9 12 9C9.79086 9 8 10.7909 8 13C8 15.2091 9.79086 17 12 17Z" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M10 7H14" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="aphm4-feature-content">
              <p class="aphm4-feature-text"><strong>True HEPA, Whole-Home Clean.</strong><br>Captures 99.9% of particles down to 0.3 microns. Covers up to 1,740 sq. ft. with a single, cost-saving filter.</p>
            </div>
          </div>
          
          <div class="aphm4-feature-item">
            <div class="aphm4-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M5 6H19C20.1046 6 21 6.89543 21 8V16C21 17.1046 20.1046 18 19 18H5C3.89543 18 3 17.1046 3 16V8C3 6.89543 3.89543 6 5 6Z" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M3 10H21" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M7 14H7.01" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M11 14H13" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="aphm4-feature-content">
              <p class="aphm4-feature-text"><strong>See the Air, Breathe Smarter.</strong><br>Infrared PM2.5 sensor with real-time display. Color-coded feedback and Auto Mode respond instantly to changing air quality.</p>
            </div>
          </div>
          
          <div class="aphm4-feature-item">
            <div class="aphm4-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11 17C11 17.5523 11.4477 18 12 18C12.5523 18 13 17.5523 13 17C13 16.4477 12.5523 16 12 16C11.4477 16 11 16.4477 11 17Z" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M11 8C11 8.55228 11.4477 9 12 9C12.5523 9 13 8.55228 13 8C13 7.44772 12.5523 7 12 7C11.4477 7 11 7.44772 11 8Z" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M18.364 5.63604C19.6150 6.88705 20.4994 8.49484 20.8264 10.2483C21.1534 12.0018 20.9039 13.8167 20.1091 15.4299C19.3143 17.0432 18.0114 18.3631 16.4078 19.1821C14.8041 20.0011 12.9821 20.2752 11.2152 19.9573C9.44824 19.6393 7.8324 18.7452 6.58938 17.3562C5.34637 15.9672 4.58598 14.1627 4.42987 12.2452C4.27376 10.3276 4.73071 8.41139 5.73036 6.7754C6.73 5.13941 8.21046 3.87956 9.95 3.17" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="aphm4-feature-content">
              <p class="aphm4-feature-text"><strong>Silent Nights, Scented Dreams.</strong><br>Runs at just 15dB in Sleep Mode with lights off at a tap. Add essential oils for a calming, personalized touch.</p>
            </div>
          </div>
          
          <div class="aphm4-feature-item">
            <div class="aphm4-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M5 4H19C20.1046 4 21 4.89543 21 6V20C21 21.1046 20.1046 22 19 22H5C3.89543 22 3 21.1046 3 20V6C3 4.89543 3.89543 4 5 4Z" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M16 2V6" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M8 2V6" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M3 10H21" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="aphm4-feature-content">
              <p class="aphm4-feature-text"><strong>Smart Control, Anywhere. Anytime.</strong><br>Connect via the Smart Life app to adjust settings, monitor air quality, and check filter life—all from your phone. Supports 2.4GHz Wi-Fi.</p>
            </div>
          </div>
          
          <div class="aphm4-feature-item">
            <div class="aphm4-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 18C15.3137 18 18 15.3137 18 12C18 8.68629 15.3137 6 12 6C8.68629 6 6 8.68629 6 12C6 15.3137 8.68629 18 12 18Z" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 2V4" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 20V22" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M4.92993 4.93005L6.33993 6.34005" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M17.6599 17.66L19.0699 19.07" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M2 12H4" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M20 12H22" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M4.92993 19.07L6.33993 17.66" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M17.6599 6.34005L19.0699 4.93005" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="aphm4-feature-content">
              <p class="aphm4-feature-text"><strong>Five Modes, One Smart Routine.</strong><br>Switch between Sleep, Auto, and 3 fan speeds. Set 2/5/8H timers for worry-free use. Child Lock keeps settings safe from little hands.</p>
            </div>
          </div>

          <div class="aphm4-feature-item">
            <div class="aphm4-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 18C15.3137 18 18 15.3137 18 12C18 8.68629 15.3137 6 12 6C8.68629 6 6 8.68629 6 12C6 15.3137 8.68629 18 12 18Z" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 2V4" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 20V22" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M4.92993 4.93005L6.33993 6.34005" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M17.6599 17.66L19.0699 19.07" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M2 12H4" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M20 12H22" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M4.92993 19.07L6.33993 17.66" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M17.6599 6.34005L19.0699 4.93005" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="aphm4-feature-content">
              <p class="aphm4-feature-text"><strong>Stay Informed, Breathe at Full Power.</strong><br>Built-in reminder and filter life display let you know exactly when to replace. Easy to swap. Use official KNKA filters for peak performance.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <button class="aphm4-modal-close" aria-label="Close">
      <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1 1L11 11M11 1L1 11" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
      </svg>
    </button>
  </div>
</div>

<script>
  // Elementor兼容的初始化函数
  function initializeElements() {
    console.log('正在初始化元素...');

    // 获取所有元素
    const learnMoreButtons = document.querySelectorAll('[data-modal]');
    const modalBackdrop = document.getElementById('modal-backdrop');
    const modals = document.querySelectorAll('.aphm4-modal');
    const closeButtons = document.querySelectorAll('.aphm4-modal-close');
    let buyButton = document.querySelector('.aphm4-button-secondary'); // 主页购买按钮
    let modalBuyButton = document.querySelector('.aphm4-modal-buy'); // 模态框购买按钮

    console.log('buyButton found:', buyButton);
    console.log('modalBuyButton found:', modalBuyButton);

    // 如果主要按钮不存在，等待一段时间后重试
    if (!buyButton) {
      console.log('buyButton未找到，1秒后重试...');
      setTimeout(initializeElements, 1000);
      return;
    }

    // 预加载图片以确保卡片高度正确
    function preloadImages() {
      const images = document.querySelectorAll('.aphm4-modal-image img');
      images.forEach(img => {
        const newImg = new Image();
        newImg.src = img.src;
      });
    }

    // 页面加载时预加载图片
    preloadImages();

    // 打开弹出卡片
    learnMoreButtons.forEach(button => {
      button.addEventListener('click', function(e) {
        e.preventDefault();
        const modalId = this.getAttribute('data-modal');
        const modal = document.getElementById(modalId);

        // 显示毛玻璃背景和弹出卡片
        modalBackdrop.classList.add('active');
        modal.classList.add('active');

        // 禁止背景滚动
        document.body.style.overflow = 'hidden';
      });
    });

    // 关闭弹出卡片
    function closeModal() {
      modalBackdrop.classList.remove('active');
      modals.forEach(modal => {
        modal.classList.remove('active');
      });

      // 恢复背景滚动
      document.body.style.overflow = '';
    }

    // 点击关闭按钮
    closeButtons.forEach(button => {
      button.addEventListener('click', closeModal);
    });

    // 点击背景关闭弹出卡片
    modalBackdrop.addEventListener('click', closeModal);

    // 阻止弹出卡片内点击事件冒泡到背景
    modals.forEach(modal => {
      modal.addEventListener('click', function(e) {
        e.stopPropagation();
      });
    });

    // ESC键关闭弹出卡片
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
        closeModal();
      }
    });

    // 1. 扩展的多语言购买链接配置
    const buyLinks = {
        // 英语国家细分
        'en-US': 'https://www.amazon.com/dp/B0D9PTN74F',
        'en-GB': 'https://amazon.co.uk/knka-dehumidifier',
        'en-CA': 'https://www.amazon.ca/dp/B0D9PTN74F',
        'en-AU': 'https://amazon.com.au/knka-dehumidifier',
        'en-NZ': 'https://amazon.com.au/knka-dehumidifier', // 新西兰用澳洲链接

        // 其他语言保持不变
        'es': 'https://www.xibanya.com',
        'de': 'https://www.faguo.com',
        'fr': 'https://www.faguo.com',
        'it': 'https://www.yidaliyu.com',

        // 默认英语
        'en': 'https://www.amazon.com/dp/B0D9PTN74F'
    };

    // 2. 获取用户国家的函数
    async function getUserCountry() {
        try {
            const response = await fetch('https://ipapi.co/json/');
            const data = await response.json();
            return data.country_code;
        } catch (error) {
            console.log('IP检测失败，使用默认美国链接');
            return 'US';
        }
    }

    // 3. 更新的购买按钮链接函数（同时更新主页和模态框按钮）
    async function updateBuyButtonLink() {
        const currentBuyButton = document.querySelector('.aphm4-button-secondary');
        const currentModalBuyButton = document.querySelector('.aphm4-modal-buy');

        if (!currentBuyButton) {
            console.error("Buy button with class '.aphm4-button-secondary' not found.");
            return;
        }

        const currentLang = document.documentElement.lang.split('-')[0];
        let targetLink;

        // 先设置默认链接，避免显示#
        const defaultLink = buyLinks['en'] || 'https://amazon.com/knka-dehumidifier';
        currentBuyButton.href = defaultLink;
        if (currentModalBuyButton) {
            currentModalBuyButton.href = defaultLink;
        }
        console.log('设置默认链接:', defaultLink);

        if (currentLang === 'en') {
            // 英语时进一步检测国家
            try {
                const country = await getUserCountry();
                const langCountry = `en-${country}`;
                targetLink = buyLinks[langCountry] || buyLinks['en'];
                console.log(`English detected. Country: ${country}, Using link: ${targetLink}`);
            } catch (error) {
                console.log('获取国家信息失败，使用默认英语链接');
                targetLink = buyLinks['en'];
            }
        } else {
            // 非英语直接使用语言代码
            targetLink = buyLinks[currentLang] || buyLinks['en'];
            console.log(`Language: ${currentLang}, Using link: ${targetLink}`);
        }

        // 同时更新两个按钮的链接
        currentBuyButton.href = targetLink;
        if (currentModalBuyButton) {
            currentModalBuyButton.href = targetLink;
            console.log('模态框按钮链接也已更新:', currentModalBuyButton.href);
        }
        console.log('主页按钮最终链接:', currentBuyButton.href);
    }

    // 4. 使用 MutationObserver 监视 <html> 标签的 lang 属性变化
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'lang') {
                updateBuyButtonLink(); // 当 lang 属性变化时，调用更新函数
            }
        });
    });

    // 5. 配置并启动观察器，让它开始监视
    observer.observe(document.documentElement, {
        attributes: true // 我们只关心属性的变化
    });

    // 6. 页面加载完成后，立即执行一次，以正确设置初始的购买链接
    updateBuyButtonLink();

    // 确保按钮和图片不重叠
    function adjustButtonImageSpacing() {
      const buttons = document.querySelectorAll('.aphm4-buttons');
      const windowWidth = window.innerWidth;

      if (windowWidth <= 480) {
        // 小屏幕上增加更多间距
        buttons.forEach(button => {
          button.style.marginBottom = '20px';
        });
      } else if (windowWidth <= 734) {
        // 移动设备上的间距
        buttons.forEach(button => {
          button.style.marginBottom = '15px';
        });
      } else if (windowWidth <= 1068) {
        // 中等屏幕上的间距
        buttons.forEach(button => {
          button.style.marginBottom = '15px';
        });
      } else {
        // 大屏幕上的间距
        buttons.forEach(button => {
          button.style.marginBottom = '15px';
        });
      }
    }

    // 页面加载和窗口大小改变时调整按钮和图片间距
    adjustButtonImageSpacing();
    window.addEventListener('resize', adjustButtonImageSpacing);
  }

  // 多种方式确保代码执行，适配Elementor环境
  document.addEventListener('DOMContentLoaded', initializeElements);
  window.addEventListener('load', initializeElements);

  // 如果页面已经加载完成，立即执行
  if (document.readyState === 'complete' || document.readyState === 'interactive') {
    setTimeout(initializeElements, 100);
  }
</script>
</body>
</html>
